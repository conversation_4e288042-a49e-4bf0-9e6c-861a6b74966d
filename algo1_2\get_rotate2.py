import cv2
import numpy as np

import cv2
import numpy as np
import yaml
import os

# -------------------------- 1. 加载相机内参 --------------------------
def load_camera_params(params_path):
    with open(params_path, "r") as f:
        params = yaml.safe_load(f)
    return (
        np.array(params["camera_matrix"]),
        np.array(params["dist_coeffs"]),
        tuple(params["image_size"])
    )
import cv2
import numpy as np

# ================== STEP 1: 请替换以下参数（你的实际数据） ==================
# 已知相机内参（你的K矩阵，3x3，单位：像素）
# 例如：fx=1000, fy=1000, cx=320, cy=240 → K = [[1000,0,320],[0,1000,240],[0,0,1]]
K, dist_coeffs, img_size = load_camera_params("../algo1_2/camera_params.yml")

# 棋盘格参数（角点数量！不是格子数量）
# 例子：8x6格子的棋盘 → 角点是9x7 → 填 (9,7)
chessboard_size = (6, 4)  # ← 重点！改成你棋盘的实际角点行列数

# 棋盘格每个格子的尺寸（单位：米，比如1cm格子填0.01）
square_size = 0.06  # ← 改成你的实际尺寸（1cm=0.01m）

# 你的棋盘格图片路径（必须是.jpg或.png！）
img_path = "D:\Dataset\keystone\cam_7.jpg"  # ← 改成你拍的图片名（比如"chess.jpg"）
# ======================================================================

# 生成3D世界坐标点（棋盘格在世界坐标系中的位置，z=0）
objp = np.zeros((np.prod(chessboard_size), 3), np.float32)
objp[:, :2] = np.mgrid[0:chessboard_size[0], 0:chessboard_size[1]].T.reshape(-1, 2)
objp *= square_size  # 按实际格子尺寸缩放

# 读取图片并转灰度
img = cv2.imread(img_path)
if img is None:
    raise FileNotFoundError(f"找不到图片: {img_path}！请确保图片在当前目录")
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

# 检测棋盘格角点（带亚像素优化）
# ret, corners = cv2.findChessboardCorners(gray, chessboard_size, None)
# if not ret:
#     raise ValueError("未检测到棋盘格角点！请重拍：确保棋盘格完整、光线均匀、无遮挡")

ret = True
corners_dict = {
    "cam_1.jpg": np.array([[502, 431], [551, 430], [604, 430], [655, 429], [707, 429], [758, 428], [500, 481], 
                             [552, 481], [603, 481], [655, 480], [709, 480], [761, 480], [497, 533], [550, 534],    
                             [603, 533], [656, 533], [709, 533], [764, 533], [494, 589], [548, 588], [602, 588], 
                             [658, 588], [711, 589], [766, 587]], dtype=np.float32),
    "cam_2.jpg": np.array([[549, 440], [602, 439], [654, 438], [706, 438], [757, 437], [809, 434], [550, 492], [602, 490],  
                             [656, 490], [708, 488], [761, 488], [814, 487], [550, 545], [602, 544], [655, 543], [710, 543], 
                             [764, 541], [817, 541], [547, 599], [603, 599], [656, 596], [712, 598], [766, 598], [822, 594]], dtype=np.float32),
    "cam_3.jpg": np.array([[471, 435], [523, 435], [574, 435], [626, 436], [676, 437], [728, 436], [469, 486], [520, 486],
                             [572, 486], [624, 487], [677, 488], [730, 489], [464, 539], [517, 539], [570, 540], [623, 541], 
                             [677, 541], [730, 542], [459, 593], [514, 594], [570, 596], [623, 594], [678, 595], [733, 597]], dtype=np.float32) ,
    "cam_7.jpg": np.array([[530, 237], [569, 258], [609, 284], [650, 305], [692, 328], [733, 351], [503, 273], [542, 298], 
                           [583, 321], [623, 344], [667, 368], [709, 395], [477, 312], [517, 336], [556, 360], [598, 384], 
                           [641, 410], [683, 437], [449, 351], [488, 376], [528, 400], [571, 425], [612, 451], [658, 479]], dtype=np.float32) 


}
#cam_1
# corners = np.array([[502, 431], [551, 430], [604, 430], [655, 429], [707, 429], [758, 428], [500, 481], 
#                         [552, 481], [603, 481], [655, 480], [709, 480], [761, 480], [497, 533], [550, 534], 
#                         [603, 533], [656, 533], [709, 533], [764, 533], [494, 589], [548, 588], [602, 588], 
#                         [658, 588], [711, 589], [766, 587]], dtype=np.float32)

#cam_2
# corners = np.array([[549, 440], [602, 439], [654, 438], [706, 438], [757, 437], [809, 434], [550, 492], [602, 490],
#                      [656, 490], [708, 488], [761, 488], [814, 487], [550, 545], [602, 544], [655, 543], [710, 543], 
#                      [764, 541], [817, 541], [547, 599], [603, 599], [656, 596], [712, 598], [766, 598], [822, 594]], dtype=np.float32)

# #cam_3
# corners = np.array([[471, 435], [523, 435], [574, 435], [626, 436], [676, 437], [728, 436], [469, 486], [520, 486],
#                     [572, 486], [624, 487], [677, 488], [730, 489], [464, 539], [517, 539], [570, 540], [623, 541], 
#                     [677, 541], [730, 542], [459, 593], [514, 594], [570, 596], [623, 594], [678, 595], [733, 597]], dtype=np.float32)

corners = corners_dict[os.path.basename(img_path)]
# 亚像素级优化角点坐标
criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
corners = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)

# 计算外参（旋转向量rvec和平移向量tvec）
ret, rvec, tvec = cv2.solvePnP(objp, corners, K, None)  # 无畸变时用None


def rotation_matrix_to_euler_angles(R):
    """
    将旋转矩阵转换为欧拉角 (Z-Y-X顺序)
    
    参数:
    R -- 3x3旋转矩阵
    
    返回:
    yaw, pitch, roll -- 旋转角度（弧度）
    """
    # 计算俯仰角 (pitch)
    pitch = -np.arcsin(R[2, 0])
    
    # 处理万向节锁情况 (当pitch接近±π/2时)
    if np.isclose(pitch, np.pi/2):
        yaw = 0
        roll = np.arctan2(R[0, 2], R[1, 2])
    elif np.isclose(pitch, -np.pi/2):
        yaw = 0
        roll = -np.arctan2(R[0, 2], R[1, 2])
    else:
        # 计算偏航角 (yaw) 和滚转角 (roll)
        yaw = np.arctan2(R[1, 0], R[0, 0])
        roll = np.arctan2(R[2, 1], R[2, 2])
    
    return yaw, pitch, roll

def calculate_plane_angles(R):
    """
    计算棋盘格平面与x-y平面、x-z平面、y-z平面的夹角
    
    参数:
    R -- 3x3旋转矩阵
    
    返回:
    angles -- 一个元组，包含与x-y平面、x-z平面、y-z平面的夹角（弧度）
    """
    # 棋盘格平面的法向量是R的第三列
    n = R[:, 2]  # 第三列
    
    # 计算与x-y平面的夹角
    angle_xy = np.arccos(np.abs(n[2]))  # 与x-y平面的夹角
    
    # 计算与x-z平面的夹角
    angle_xz = np.arccos(np.abs(n[1]))  # 与x-z平面的夹角
    
    # 计算与y-z平面的夹角
    angle_yz = np.arccos(np.abs(n[0]))  # 与y-z平面的夹角
    
    return angle_xy, angle_xz, angle_yz
    
if ret:
    # 将旋转向量转为旋转矩阵（3x3）
    rmat, _ = cv2.Rodrigues(rvec)
    
    # 打印结果（外参就是rmat和tvec！）
    print("\n✅ 棋盘格相对于相机的外参计算成功！")
    print("旋转矩阵 R (3x3):")
    print(rmat)
    print("\n平移向量 t (3x1, 单位: 米):")
    print(tvec)

    yaw, pitch, roll = rotation_matrix_to_euler_angles(rmat)

    import math
    # 转换为角度制（弧度转角度）
    yaw_deg = math.degrees(yaw)
    pitch_deg = math.degrees(pitch)
    roll_deg = math.degrees(roll)

    print("欧拉角 (Z-Y-X顺序):")
    print(f"偏航角 (Yaw): {yaw_deg:.2f} 度")
    print(f"俯仰角 (Pitch): {pitch_deg:.2f} 度")
    print(f"滚转角 (Roll): {roll_deg:.2f} 度")

    
    # 可视化效果（选做，帮你确认结果）
    cv2.drawChessboardCorners(img, chessboard_size, corners, ret)
    cv2.imshow("Detected Chessboard", img)
    cv2.waitKey(0)
else:
    print("❌ solvePnP失败！请检查内参或图片质量")